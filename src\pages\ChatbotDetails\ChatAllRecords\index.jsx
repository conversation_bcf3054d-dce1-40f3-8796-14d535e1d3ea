import { useState } from 'react';
import { DateTime } from 'luxon';
import { useNavigate, useParams } from 'react-router-dom';
import DButton from '@/components/Global/DButton';
import DSelect from '@/components/Global/DSelect';
import ExportIcon from '@/components/Global/Icons/ExportIcon';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import DDateRangePicker from '@/components/DDateRangePicker';
import DModalExportFormat from '@/components/DModalExportFormat';
import useDanteApi from '@/hooks/useDanteApi';
import * as conversationService from '@/services/conversations.service';
import { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';
import useToast from '@/hooks/useToast';
import { ListboxOption } from '@headlessui/react';
import DLoading from '@/components/DLoading';
import DLoaderWriting from '@/components/Global/DLoaderWriting';

const ChatAllRecords = () => {
  const navigate = useNavigate();
  const params = useParams();
  const { addErrorToast } = useToast();
  const timezone = useTimezoneStore((state) => state.timezone);

  const [isLoadingRecords, setIsLoadingRecords] = useState(false);
  const [dateSelectedRecords, setDateSelectedRecords] = useState('Last 30 days');
  const [exportFormatModal, setExportFormatModal] = useState({
    isOpen: false,
    type: ''
  });
  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now()
      .minus({ days: 30 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    date_to: DateTime.now().endOf('day').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  });
  const [customDateRange, setCustomDateRange] = useState({
    from: null,
    to: null,
  });

  const filterOptions = [
    {
      label: 'Week to date',
      value: DateTime.now()
        .startOf('week')
        .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    },
    {
      label: 'Month to date',
      value: DateTime.now()
        .startOf('month')
        .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    },
    {
      label: 'Last 7 days',
      value: DateTime.now()
        .minus({ days: 7 })
        .startOf('day')
        .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    },
    {
      label: 'Last 30 days',
      value: DateTime.now()
        .minus({ days: 30 })
        .startOf('day')
        .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    },
    {
      label: 'Last 90 days',
      value: DateTime.now()
        .minus({ days: 90 })
        .startOf('day')
        .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    },
    {
      label: 'Custom',
      value: 'custom',
    },
  ];

  const { data, isLoading } = useDanteApi(
    conversationService.getSharedConversations,
    [timeRange],
    {},
    params.id,
    timeRange.date_from,
    timeRange.date_to,
    timezone
  );

  const handleDateRangeChange = (fromDate, toDate) => {
    setTimeRange({
      date_from: DateTime.fromISO(fromDate).toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
      date_to: DateTime.fromISO(toDate).toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
      label: 'custom'
    });
  };

  const handleExportRecords = async (format, type = 'by_date') => {
    setIsLoadingRecords(true);
    try {
      if (data.results.length === 0) {
        addErrorToast({ message: 'No records found' });
        return;
      }
      const dateFrom = type === 'all'
        ? DateTime.now().startOf('year').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS")
        : timeRange.date_from;
      const dateTo = type === 'all'
        ? DateTime.now().endOf('day').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS")
        : timeRange.date_to;

      const response = await conversationService.downloadChatRecordLog(
        params.id,
        dateFrom,
        dateTo,
        format,
        timezone
      );
      if (response.status === 200) {
        const download_url = response.data.download_url;
        window.open(download_url, '_blank');
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingRecords(false);
      setExportFormatModal({ isOpen: false, type: '' });
    }
  };

  const handleExportFormat = (format) => {
    handleExportRecords(format, 'by_date');
  };

  // Show initial loading only when there's no data at all
  if (!data && isLoading) {
    return <DLoading show={true} />
  }

  return (
    <div className="w-full h-full bg-white rounded-size5 p-size5 flex flex-col gap-size2 overflow-hidden">
      <div className="flex justify-between items-center">
        <p className="text-xl font-medium tracking-tight">Chat records</p>
      </div>
      <div className="flex gap-size1">
        <DSelect
          options={filterOptions}
          onChange={(value) => {
            const selectedLabel = filterOptions.find(option => option.value === value)?.label || 'custom';
            setDateSelectedRecords(selectedLabel);
            if (value !== 'custom') {
              setTimeRange({
                ...timeRange,
                date_from: value,
                date_to: DateTime.now().endOf('day').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
              });
              setCustomDateRange({ from: null, to: null });
            } else {
              // Reset custom date range when switching to custom
              setCustomDateRange({ from: null, to: null });
            }
          }}
          selectedChild={dateSelectedRecords}
          listButtonClass="!w-auto !h-[40px]"
        >
          <ListboxOption>Last 30 days</ListboxOption>
        </DSelect>
        {dateSelectedRecords?.toLowerCase() === 'custom' && (
          <DDateRangePicker
            fromDate={timeRange.date_from}
            toDate={timeRange.date_to}
            onApply={(from, to) => {
              setTimeRange({
                date_from: DateTime.fromISO(from).toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
                date_to: DateTime.fromISO(to).toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
                label: 'custom'
              });
            }}
          />
        )}
        <DButton
          variant="grey"
          className="!h-[40px] !w-32"
          onClick={() => setExportFormatModal({ isOpen: true, type: 'records' })}
          loading={isLoadingRecords}
        >
          <ExportIcon />
          <p className="text-base tracking-tight">Export</p>
        </DButton>
      </div>
      <div className="flex flex-col gap-size1 w-full grow overflow-y-auto no-scrollbar relative">
        {/* Show inline loader when data is being refetched */}
        {isLoading && data && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
            <div className="flex items-center gap-2">
              <DLoaderWriting />
              <span className="text-sm text-grey-75">Loading records...</span>
            </div>
          </div>
        )}
        {data?.results?.map((item, index) => (
          <div
            className={`flex justify-between items-center p-size1 ${
              item.has_not_answered_questions
                ? 'bg-negative-2 text-negative-100'
                : 'bg-transparent text-black'
            } border-b border-b-grey-5 `}
            key={index}
          >
            <p className="text-base tracking-tight">
              {item?.medium === 'whatsapp' ? item?.name : DateTime.fromISO(item.date_created).toFormat(
                'd MMM yyyy - HH:mm:ss'
              )}
            </p>
            <div className="flex items-center gap-size1">
              {item.has_not_answered_questions && checkFeatureAvailability('unanswered_question_recognition') && (
                <FlagIcon className="text-negative-100" />
              )}
              <button onClick={() => navigate(`${item.id}`)}>
                <UpRightIcon />
              </button>
            </div>
          </div>
        ))}
        {/* Show message when no data is available */}
        {data && (!data.results || data.results.length === 0) && !isLoading && (
          <div className="flex items-center justify-center h-32 text-grey-50">
            <p>No chat records found for the selected time period.</p>
          </div>
        )}
      </div>
      <DModalExportFormat
        open={exportFormatModal.isOpen}
        onClose={() => setExportFormatModal({ isOpen: false, type: '' })}
        onExport={handleExportFormat}
        loading={isLoadingRecords}
      />
    </div>
  );
};

export default ChatAllRecords;